"use client"

import { useChat } from "@ai-sdk/react"
import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"

type AnyPart = {
  type: string
  text?: string
  args?: unknown
  result?: unknown
  output?: unknown
  toolName?: string
}

export default function Home() {
  const { messages, status, sendMessage } = useChat({ api: "/api/chat" })
  const [input, setInput] = useState("")
  const [listings, setListings] = useState<any[]>([]) // extracted listings
  const listRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    listRef.current?.scrollTo({ top: listRef.current.scrollHeight, behavior: "smooth" })
  }, [messages])

  // Extract tool outputs into cards
  useEffect(() => {
    const newListings: any[] = []
    messages.forEach((m: any) => {
      if (Array.isArray(m.parts)) {
        m.parts.forEach((part: AnyPart) => {
          if (part.type?.startsWith("tool-")) {
            const payload = part.result ?? part.output ?? null
            if (Array.isArray(payload)) {
              newListings.push(...payload)
            } else if (payload) {
              newListings.push(payload)
            }
          }
        })
      }
    })
    setListings(newListings)
  }, [messages])

  return (
    <main className="mx-auto max-w-6xl p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Chat Window */}
      <div className="flex flex-col">
        <header className="mb-4">
          <h1 className="text-2xl font-semibold text-balance">Find your dream home</h1>
          <p className="text-sm text-muted-foreground">
            Tell me your budget, rooms, location, furnishing, facing, and when you can move in.
          </p>
        </header>

        <Card className="flex-1 mb-4 h-[60vh] overflow-hidden">
          <CardContent className="p-0 h-full">
            <div ref={listRef} className="h-full overflow-y-auto p-4 space-y-3">
              {messages.map((m) => (
                <div
                  key={m.id}
                  className={m.role === "user" ? "flex justify-end" : "flex justify-start"}
                >
                  <div
                    className={
                      m.role === "user"
                        ? "rounded-lg bg-primary text-primary-foreground px-3 py-2 max-w-[80%]"
                        : "rounded-lg bg-muted px-3 py-2 max-w-[80%]"
                    }
                  >
                    <MessageContent parts={(m as any).parts} content={(m as any).content} />
                  </div>
                </div>
              ))}
              {(status === "submitted" || status === "streaming") && <div className="text-sm text-muted-foreground">Searching listings…</div>}
            </div>
          </CardContent>
        </Card>

        <form
          onSubmit={(e) => {
            e.preventDefault()
            const text = input.trim()
            if (!text) return
            sendMessage({ text })
            setInput("")
          }}
          className="flex items-center gap-2"
        >
          <Input
            value={input}
            onChange={(e) => setInput(e.currentTarget.value)}
            placeholder="e.g., 2BHK in Whitefield, budget ₹65k, semi-furnished, Oct move-in"
            aria-label="Message"
          />
          <Button type="submit" disabled={status === "submitted" || status === "streaming"}>
            {status === "submitted" || status === "streaming" ? "Thinking..." : "Send"}
          </Button>
        </form>
      </div>

      {/* Listings Sidebar */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Listings</h2>
        {listings.length === 0 && (
          <p className="text-sm text-muted-foreground">No listings yet. Ask something!</p>
        )}
        {listings.map((listing, idx) => (
          <Card key={idx}>
            <CardContent className="p-4">
              <p className="font-medium">{listing["Helium Name / ID"] ?? "Unknown property"}</p>
              <p className="text-sm text-muted-foreground">{listing.Society}</p>
              <p className="text-sm">Rent: ₹{listing.Rent}</p>
              <p className="text-sm">Rooms: {listing["# Rooms"]}</p>
              <p className="text-sm">Facing: {listing.Facing}</p>
            </CardContent>
          </Card>
        ))}
      </div>
    </main>
  )
}

function MessageContent({ parts, content }: { parts?: any[]; content?: string }) {
  if (Array.isArray(parts) && parts.length > 0) {
    return (
      <div className="space-y-2">
        {parts.map((part, i) => {
          if (part.type === "text" && typeof part.text === "string") {
            return (
              <p key={i} className="text-pretty text-sm leading-6">
                {part.text}
              </p>
            )
          }
          // skip tool JSON here – cards handle it
          return null
        })}
      </div>
    )
  }

  if (typeof content === "string") {
    return <p className="text-pretty text-sm leading-6">{content}</p>
  }

  return null
}
