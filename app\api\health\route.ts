// Simple health check route to verify envs (no secrets leaked)
export async function GET() {
  const hasUrl = Boolean(process.env.REAL_ESTATE_API_URL)
  const hasKey = Boolean(process.env.REAL_ESTATE_API_KEY)
  return new Response(
    JSON.stringify({
      ok: true,
      apiUrlConfigured: hasUrl,
      apiKeyConfigured: hasKey,
      timestamp: new Date().toISOString(),
    }),
    { headers: { "Content-Type": "application/json" } },
  )
}
